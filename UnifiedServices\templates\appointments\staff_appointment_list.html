{% extends 'base.html' %}

{% block title %}Appointment Management - JHCSC Unified Student Services{% endblock %}

{% block page_title %}Appointment Management{% endblock %}

{% block mobile_nav %}
{% include 'navigation/staff_nav.html' %}
{% endblock %}

{% block desktop_nav %}
{% include 'navigation/staff_nav.html' %}
{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto">
    <!-- Header with Stats -->
    <div class="mb-8">
        <div class="sm:flex sm:items-center sm:justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">{{ department.name }} Appointments</h1>
                <p class="mt-2 text-sm text-gray-700">Manage and review student service requests</p>
            </div>
        </div>
        
        <!-- Stats Cards -->
        <div class="mt-6 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-6 w-6 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Pending</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ pending_count }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-6 w-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99" />
                            </svg>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">In Progress</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ in_progress_count }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-6 w-6 text-green-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Ready</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ ready_count }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M11.35 3.836c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m8.25 2.25h.008v.008h-.008V10.5zM8.25 18.75h1.5v.008h-.008v-.008h-.008v.008h-.008V18.75zM8.25 12h.008v.008H8.25V12zm0 3h.008v.008H8.25V15zm0 3h.008v.008H8.25V18z" />
                            </svg>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Claimed</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ claimed_count }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex flex-wrap gap-2">
                <a href="{% url 'staff_appointment_list' %}" 
                   class="inline-flex items-center px-3 py-2 border text-sm font-medium rounded-md 
                          {% if current_status == 'all' %}border-indigo-500 text-indigo-700 bg-indigo-50{% else %}border-gray-300 text-gray-700 bg-white hover:bg-gray-50{% endif %}">
                    All Appointments
                </a>
                <a href="{% url 'staff_appointment_list' %}?status=pending" 
                   class="inline-flex items-center px-3 py-2 border text-sm font-medium rounded-md 
                          {% if current_status == 'pending' %}border-yellow-500 text-yellow-700 bg-yellow-50{% else %}border-gray-300 text-gray-700 bg-white hover:bg-gray-50{% endif %}">
                    Pending ({{ pending_count }})
                </a>
                <a href="{% url 'staff_appointment_list' %}?status=in_progress" 
                   class="inline-flex items-center px-3 py-2 border text-sm font-medium rounded-md 
                          {% if current_status == 'in_progress' %}border-blue-500 text-blue-700 bg-blue-50{% else %}border-gray-300 text-gray-700 bg-white hover:bg-gray-50{% endif %}">
                    In Progress ({{ in_progress_count }})
                </a>
                <a href="{% url 'staff_appointment_list' %}?status=ready" 
                   class="inline-flex items-center px-3 py-2 border text-sm font-medium rounded-md 
                          {% if current_status == 'ready' %}border-green-500 text-green-700 bg-green-50{% else %}border-gray-300 text-gray-700 bg-white hover:bg-gray-50{% endif %}">
                    Ready ({{ ready_count }})
                </a>
                <a href="{% url 'staff_appointment_list' %}?status=claimed" 
                   class="inline-flex items-center px-3 py-2 border text-sm font-medium rounded-md 
                          {% if current_status == 'claimed' %}border-gray-500 text-gray-700 bg-gray-50{% else %}border-gray-300 text-gray-700 bg-white hover:bg-gray-50{% endif %}">
                    Claimed ({{ claimed_count }})
                </a>
            </div>
        </div>
    </div>

    <!-- Appointments Table -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            {% if appointments %}
            <div class="overflow-hidden">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Service</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Progress</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                            <th class="relative px-6 py-3"><span class="sr-only">Actions</span></th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for appointment in appointments %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <div class="h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center">
                                            <span class="text-sm font-medium text-indigo-700">
                                                {{ appointment.student.first_name|first }}{{ appointment.student.last_name|first }}
                                            </span>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">{{ appointment.student.get_full_name }}</div>
                                        <div class="text-sm text-gray-500">{{ appointment.appointment_id }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ appointment.service.name }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                    {% if appointment.status == 'pending' %}bg-yellow-100 text-yellow-800
                                    {% elif appointment.status == 'in_progress' %}bg-blue-100 text-blue-800
                                    {% elif appointment.status == 'ready' %}bg-green-100 text-green-800
                                    {% elif appointment.status == 'claimed' %}bg-gray-100 text-gray-800
                                    {% endif %}">
                                    {{ appointment.get_status_display }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {% with total=appointment.requirements.count %}
                                {% with completed=appointment.requirements.all %}
                                <div class="flex items-center">
                                    <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                        {% if total > 0 %}
                                        <div class="bg-indigo-600 h-2 rounded-full" style="width: {{ appointment.completion_percentage }}%"></div>
                                        {% else %}
                                        <div class="bg-indigo-600 h-2 rounded-full" style="width: 0%"></div>
                                        {% endif %}
                                    </div>
                                    <span class="text-xs">{{ appointment.completed_requirements_count }}/{{ total }}</span>
                                </div>
                                {% endwith %}
                                {% endwith %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ appointment.created_at|date:"M d, Y" }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <a href="{% url 'staff_appointment_detail' appointment.appointment_id %}" 
                                   class="text-indigo-600 hover:text-indigo-900">Manage</a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if is_paginated %}
            <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                <div class="flex-1 flex justify-between sm:hidden">
                    {% if page_obj.has_previous %}
                    <a href="?page={{ page_obj.previous_page_number }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">Previous</a>
                    {% endif %}
                    {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">Next</a>
                    {% endif %}
                </div>
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">
                            Showing <span class="font-medium">{{ page_obj.start_index }}</span> to <span class="font-medium">{{ page_obj.end_index }}</span> of <span class="font-medium">{{ page_obj.paginator.count }}</span> results
                        </p>
                    </div>
                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                            {% if page_obj.has_previous %}
                            <a href="?page={{ page_obj.previous_page_number }}" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">Previous</a>
                            {% endif %}
                            {% if page_obj.has_next %}
                            <a href="?page={{ page_obj.next_page_number }}" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">Next</a>
                            {% endif %}
                        </nav>
                    </div>
                </div>
            </div>
            {% endif %}
            
            {% else %}
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 002.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25zM6.75 12h.008v.008H6.75V12zm0 3h.008v.008H6.75V15zm0 3h.008v.008H6.75V18z" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No appointments</h3>
                <p class="mt-1 text-sm text-gray-500">
                    {% if current_status != 'all' %}
                        No appointments found with status "{{ current_status }}".
                    {% else %}
                        No appointments found for your department.
                    {% endif %}
                </p>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
