{% extends 'base.html' %}

{% block title %}{{ service.name }} - JHCSC Unified Student Services{% endblock %}

{% block page_title %}Service Details{% endblock %}

{% block mobile_nav %}
{% include 'navigation/student_nav.html' %}
{% endblock %}

{% block desktop_nav %}
{% include 'navigation/student_nav.html' %}
{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto">
    <!-- Breadcrumb -->
    <nav class="flex mb-8" aria-label="Breadcrumb">
        <ol role="list" class="flex items-center space-x-4">
            <li>
                <div>
                    <a href="{% url 'student_dashboard' %}" class="text-gray-400 hover:text-gray-500">
                        <svg class="flex-shrink-0 h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
                        </svg>
                        <span class="sr-only">Home</span>
                    </a>
                </div>
            </li>
            <li>
                <div class="flex items-center">
                    <svg class="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
                    </svg>
                    <a href="{% url 'service_list' %}" class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700">Services</a>
                </div>
            </li>
            <li>
                <div class="flex items-center">
                    <svg class="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
                    </svg>
                    <span class="ml-4 text-sm font-medium text-gray-500">{{ service.name }}</span>
                </div>
            </li>
        </ol>
    </nav>

    <!-- Service Header -->
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex items-start justify-between">
                <div class="flex items-start space-x-4">
                    <div class="flex-shrink-0">
                        <div class="h-16 w-16 rounded-full bg-indigo-100 flex items-center justify-center">
                            <span class="text-lg font-medium text-indigo-600">{{ service.department.code }}</span>
                        </div>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">{{ service.name }}</h1>
                        <p class="mt-1 text-sm text-gray-500">{{ service.department.name }}</p>
                        <div class="mt-2 flex items-center text-sm text-gray-500">
                            <svg class="flex-shrink-0 mr-1.5 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            Processing Time: {{ service.processing_time }}
                        </div>
                    </div>
                </div>
                <div class="flex-shrink-0">
                    <a href="{% url 'appointment_create' %}?service={{ service.id }}" 
                       class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.5v15m7.5-7.5h-15" />
                        </svg>
                        Request This Service
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Description -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Description</h3>
                    <p class="text-gray-700">{{ service.description }}</p>
                </div>
            </div>

            <!-- Requirements -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Requirements</h3>
                    {% if service.requirements.all %}
                    <div class="space-y-4">
                        {% for requirement in service.requirements.all %}
                        <div class="border border-gray-200 rounded-lg p-4">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <h4 class="text-sm font-medium text-gray-900">{{ requirement.name }}</h4>
                                    <p class="mt-1 text-sm text-gray-500">{{ requirement.description }}</p>
                                </div>
                                <div class="ml-4 flex-shrink-0">
                                    {% if requirement.requires_upload %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        <svg class="-ml-0.5 mr-1.5 h-2 w-2 text-blue-400" fill="currentColor" viewBox="0 0 8 8">
                                            <circle cx="4" cy="4" r="3" />
                                        </svg>
                                        Upload Required
                                    </span>
                                    {% else %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        No Upload
                                    </span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <p class="text-gray-500 text-sm">No specific requirements for this service.</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Quick Info -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Quick Information</h3>
                    <dl class="space-y-3">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Department</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ service.department.name }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Department Code</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ service.department.code }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Processing Time</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ service.processing_time }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Requirements</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ service.requirements.count }} requirement{{ service.requirements.count|pluralize }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Upload Required</dt>
                            <dd class="mt-1 text-sm text-gray-900">
                                {% if service.requirements.filter:requires_upload=True %}
                                Yes ({{ service.requirements.filter:requires_upload=True.count }} file{{ service.requirements.filter:requires_upload=True.count|pluralize }})
                                {% else %}
                                No
                                {% endif %}
                            </dd>
                        </div>
                    </dl>
                </div>
            </div>

            <!-- Department Info -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Department Information</h3>
                    <p class="text-sm text-gray-600 mb-4">{{ service.department.description }}</p>
                    
                    <div class="border-t border-gray-200 pt-4">
                        <h4 class="text-sm font-medium text-gray-900 mb-2">Other Services</h4>
                        {% with other_services=service.department.services.all %}
                        {% if other_services.count > 1 %}
                        <ul class="space-y-2">
                            {% for other_service in other_services %}
                            {% if other_service != service %}
                            <li>
                                <a href="{% url 'service_detail' other_service.pk %}" 
                                   class="text-sm text-indigo-600 hover:text-indigo-500">
                                    {{ other_service.name }}
                                </a>
                            </li>
                            {% endif %}
                            {% endfor %}
                        </ul>
                        {% else %}
                        <p class="text-sm text-gray-500">This is the only service offered by this department.</p>
                        {% endif %}
                        {% endwith %}
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Actions</h3>
                    <div class="space-y-3">
                        <a href="{% url 'appointment_create' %}?service={{ service.id }}" 
                           class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Request This Service
                        </a>
                        <a href="{% url 'service_list' %}" 
                           class="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Browse All Services
                        </a>
                        <a href="{% url 'service_list' %}?department={{ service.department.id }}" 
                           class="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            View Department Services
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
