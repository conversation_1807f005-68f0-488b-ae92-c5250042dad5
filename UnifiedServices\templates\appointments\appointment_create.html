{% extends 'base.html' %}

{% block title %}Create Appointment - JHCSC Unified Student Services{% endblock %}

{% block page_title %}Create New Appointment{% endblock %}

{% block extra_head %}
<!-- Custom Tailwind Config for Emerald & Gold Theme -->
<script>
    tailwind.config = {
        theme: {
            extend: {
                colors: {
                    emerald: {
                        50: '#ecfdf5',
                        100: '#d1fae5',
                        200: '#a7f3d0',
                        300: '#6ee7b7',
                        400: '#34d399',
                        500: '#10b981',
                        600: '#059669',
                        700: '#047857',
                        800: '#065f46',
                        900: '#064e3b',
                        950: '#022c22'
                    },
                    gold: {
                        50: '#fffbeb',
                        100: '#fef3c7',
                        200: '#fde68a',
                        300: '#fcd34d',
                        400: '#fbbf24',
                        500: '#f59e0b',
                        600: '#d97706',
                        700: '#b45309',
                        800: '#92400e',
                        900: '#78350f'
                    }
                },
                animation: {
                    'fade-in': 'fadeIn 0.6s ease-out',
                    'slide-up': 'slideUp 0.6s ease-out',
                    'card-hover': 'cardHover 0.3s ease',
                    'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                },
                keyframes: {
                    fadeIn: {
                        '0%': { opacity: '0' },
                        '100%': { opacity: '1' }
                    },
                    slideUp: {
                        '0%': { opacity: '0', transform: 'translateY(20px)' },
                        '100%': { opacity: '1', transform: 'translateY(0)' }
                    },
                    cardHover: {
                        '0%': { transform: 'translateY(0)' },
                        '100%': { transform: 'translateY(-4px)' }
                    }
                }
            }
        }
    }
</script>

<style>
    [x-cloak] { display: none !important; }

    .card-hover {
        transition: all 0.3s ease;
    }

    .card-hover:hover {
        transform: translateY(-4px);
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    .form-input {
        transition: all 0.2s ease;
    }

    .form-input:focus {
        transform: translateY(-1px);
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }

    .gradient-border {
        background: linear-gradient(white, white) padding-box,
                    linear-gradient(135deg, #059669, #f59e0b) border-box;
        border: 2px solid transparent;
    }
</style>
{% endblock %}

{% block mobile_nav %}
{% include 'navigation/student_nav.html' %}
{% endblock %}

{% block desktop_nav %}
{% include 'navigation/student_nav.html' %}
{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto animate-fade-in">
    <!-- Header Section -->
    <div class="bg-gradient-to-r from-emerald-600 to-emerald-700 rounded-2xl p-6 mb-8 text-white">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold mb-2">Create New Appointment</h1>
                <p class="text-emerald-100">Request a service from our available departments</p>
            </div>
            <div class="hidden md:block">
                <div class="w-16 h-16 bg-white/10 rounded-full flex items-center justify-center">
                    <svg class="w-8 h-8 text-gold-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.5v15m7.5-7.5h-15" />
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- Breadcrumb -->
    <nav class="flex mb-8" aria-label="Breadcrumb">
        <ol role="list" class="flex items-center space-x-4">
            <li>
                <div>
                    <a href="{% url 'student_dashboard' %}" class="text-emerald-600 hover:text-emerald-700 transition-colors duration-200">
                        <svg class="flex-shrink-0 h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
                        </svg>
                        <span class="sr-only">Home</span>
                    </a>
                </div>
            </li>
            <li>
                <div class="flex items-center">
                    <svg class="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
                    </svg>
                    <span class="ml-4 text-sm font-medium text-gray-500">Create Appointment</span>
                </div>
            </li>
        </ol>
    </nav>

    <!-- Form -->
    <div class="bg-white shadow-xl rounded-2xl border border-gray-100 animate-slide-up">
        <div class="px-8 py-10">
            <div class="flex items-center mb-8">
                <div class="w-12 h-12 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-xl flex items-center justify-center shadow-lg mr-4">
                    <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                </div>
                <div>
                    <h3 class="text-2xl font-bold text-gray-900">Service Request Form</h3>
                    <p class="text-gray-600">Fill out the details below to request a new service</p>
                </div>
            </div>
            
            <form method="post" class="space-y-8" x-data="{ selectedService: '', showServiceInfo: false }">
                {% csrf_token %}

                <!-- Service Selection -->
                <div class="space-y-4">
                    <label for="{{ form.service.id_for_label }}" class="flex items-center text-lg font-semibold text-gray-900">
                        <div class="w-8 h-8 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-lg flex items-center justify-center mr-3">
                            <svg class="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                            </svg>
                        </div>
                        Select Service *
                    </label>
                    <div class="relative">
                        <select id="{{ form.service.id_for_label }}"
                                name="{{ form.service.name }}"
                                required
                                x-model="selectedService"
                                @change="updateServiceInfo(); showServiceInfo = selectedService !== ''"
                                class="form-input block w-full rounded-xl border-2 border-gray-200 bg-white px-4 py-4 text-lg shadow-sm focus:border-emerald-500 focus:ring-emerald-500 transition-all duration-200 hover:border-emerald-300">
                            <option value="">Choose a service from the available departments...</option>
                            {% for service in form.service.field.queryset %}
                            <option value="{{ service.id }}"
                                    data-department="{{ service.department.name }}"
                                    data-description="{{ service.description }}"
                                    data-processing-time="{{ service.processing_time }}">
                                {{ service.department.name }} - {{ service.name }}
                            </option>
                            {% endfor %}
                        </select>
                        <div class="absolute inset-y-0 right-0 flex items-center pr-4 pointer-events-none">
                            <svg class="w-5 h-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </div>
                    </div>
                    {% if form.service.errors %}
                        <div class="flex items-center mt-2 text-red-600">
                            <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <p class="text-sm">{{ form.service.errors.0 }}</p>
                        </div>
                    {% endif %}
                </div>

                <!-- Service Information (shown when service is selected) -->
                <div x-show="showServiceInfo"
                     x-transition:enter="transition ease-out duration-300"
                     x-transition:enter-start="opacity-0 transform scale-95"
                     x-transition:enter-end="opacity-100 transform scale-100"
                     x-transition:leave="transition ease-in duration-200"
                     x-transition:leave-start="opacity-100 transform scale-100"
                     x-transition:leave-end="opacity-0 transform scale-95"
                     id="service-info"
                     class="bg-gradient-to-r from-emerald-50 to-gold-50 border-2 border-emerald-200 rounded-xl p-6 card-hover">
                    <div class="flex items-center mb-4">
                        <div class="w-10 h-10 bg-gradient-to-br from-emerald-500 to-gold-500 rounded-lg flex items-center justify-center mr-3">
                            <svg class="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <h4 class="text-lg font-semibold text-gray-900">Service Details</h4>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div class="bg-white rounded-lg p-4 shadow-sm">
                            <p class="font-medium text-gray-700 mb-1">Department</p>
                            <p class="text-emerald-600 font-semibold" id="service-department"></p>
                        </div>
                        <div class="bg-white rounded-lg p-4 shadow-sm">
                            <p class="font-medium text-gray-700 mb-1">Processing Time</p>
                            <p class="text-gold-600 font-semibold" id="service-processing-time"></p>
                        </div>
                        <div class="bg-white rounded-lg p-4 shadow-sm md:col-span-1">
                            <p class="font-medium text-gray-700 mb-1">Description</p>
                            <p class="text-gray-600" id="service-description"></p>
                        </div>
                    </div>
                </div>

                <!-- Notes -->
                <div class="space-y-4">
                    <label for="{{ form.notes.id_for_label }}" class="flex items-center text-lg font-semibold text-gray-900">
                        <div class="w-8 h-8 bg-gradient-to-br from-gold-500 to-gold-600 rounded-lg flex items-center justify-center mr-3">
                            <svg class="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                            </svg>
                        </div>
                        Additional Notes
                        <span class="ml-2 text-sm font-normal text-gray-500">(Optional)</span>
                    </label>
                    <div class="relative">
                        <textarea id="{{ form.notes.id_for_label }}"
                                  name="{{ form.notes.name }}"
                                  rows="4"
                                  class="form-input block w-full rounded-xl border-2 border-gray-200 bg-white px-4 py-4 text-lg shadow-sm focus:border-emerald-500 focus:ring-emerald-500 transition-all duration-200 hover:border-emerald-300 resize-none"
                                  placeholder="Share any additional information, special requests, or specific requirements for your service request...">{{ form.notes.value|default:'' }}</textarea>
                        <div class="absolute bottom-3 right-3 text-xs text-gray-400">
                            <span id="char-count">0</span> characters
                        </div>
                    </div>
                    {% if form.notes.errors %}
                        <div class="flex items-center mt-2 text-red-600">
                            <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <p class="text-sm">{{ form.notes.errors.0 }}</p>
                        </div>
                    {% endif %}
                </div>

                <!-- Form Actions -->
                <div class="flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-4 pt-6 border-t border-gray-200">
                    <a href="{% url 'student_dashboard' %}"
                       class="inline-flex items-center justify-center px-6 py-3 border-2 border-gray-300 rounded-xl text-lg font-semibold text-gray-700 bg-white hover:bg-gray-50 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200 card-hover">
                        <svg class="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                        Cancel
                    </a>
                    <button type="submit"
                            class="inline-flex items-center justify-center px-8 py-3 border border-transparent rounded-xl text-lg font-semibold text-white bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-700 hover:to-emerald-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 transition-all duration-200 shadow-lg hover:shadow-xl card-hover">
                        <svg class="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.5v15m7.5-7.5h-15" />
                        </svg>
                        Create Appointment
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function updateServiceInfo() {
    const select = document.querySelector('select[name="service"]');
    const selectedOption = select.options[select.selectedIndex];

    if (selectedOption.value) {
        document.getElementById('service-department').textContent = selectedOption.dataset.department;
        document.getElementById('service-description').textContent = selectedOption.dataset.description;
        document.getElementById('service-processing-time').textContent = selectedOption.dataset.processingTime;
    }
}

// Character counter for notes textarea
document.addEventListener('DOMContentLoaded', function() {
    const textarea = document.querySelector('textarea[name="notes"]');
    const charCount = document.getElementById('char-count');

    if (textarea && charCount) {
        function updateCharCount() {
            charCount.textContent = textarea.value.length;
        }

        textarea.addEventListener('input', updateCharCount);
        updateCharCount(); // Initial count
    }

    // Add smooth focus animations
    const inputs = document.querySelectorAll('.form-input');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('scale-[1.02]');
        });

        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('scale-[1.02]');
        });
    });
});
</script>
{% endblock %}
