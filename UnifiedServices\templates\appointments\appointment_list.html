{% extends 'base.html' %}

{% block title %}My Appointments - JHCSC Unified Student Services{% endblock %}

{% block page_title %}My Appointments{% endblock %}

{% block mobile_nav %}
{% include 'navigation/student_nav.html' %}
{% endblock %}

{% block desktop_nav %}
{% include 'navigation/student_nav.html' %}
{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto">
    <!-- Header -->
    <div class="sm:flex sm:items-center mb-8">
        <div class="sm:flex-auto">
            <h1 class="text-2xl font-semibold text-gray-900">My Appointments</h1>
            <p class="mt-2 text-sm text-gray-700">Track the status of your service requests and appointments.</p>
        </div>
        <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
            <a href="{% url 'appointment_create' %}" 
               class="inline-flex items-center justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:w-auto">
                <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.5v15m7.5-7.5h-15" />
                </svg>
                New Appointment
            </a>
        </div>
    </div>

    {% if appointments %}
    <!-- Appointments list -->
    <div class="bg-white shadow overflow-hidden sm:rounded-md">
        <ul role="list" class="divide-y divide-gray-200">
            {% for appointment in appointments %}
            <li>
                <a href="{% url 'appointment_detail' appointment.pk %}" class="block hover:bg-gray-50">
                    <div class="px-4 py-4 sm:px-6">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                                        <span class="text-sm font-medium text-gray-600">{{ appointment.service.department.code }}</span>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <div class="flex items-center">
                                        <p class="text-sm font-medium text-indigo-600 truncate">
                                            {{ appointment.service.name }}
                                        </p>
                                        <p class="ml-2 text-sm text-gray-500">
                                            #{{ appointment.appointment_id }}
                                        </p>
                                    </div>
                                    <div class="mt-1 flex items-center text-sm text-gray-500">
                                        <p>{{ appointment.service.department.name }}</p>
                                        <span class="mx-2">•</span>
                                        <p>{{ appointment.created_at|date:"M d, Y" }}</p>
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    {% if appointment.status == 'pending' %}bg-yellow-100 text-yellow-800
                                    {% elif appointment.status == 'in_progress' %}bg-blue-100 text-blue-800
                                    {% elif appointment.status == 'ready' %}bg-green-100 text-green-800
                                    {% elif appointment.status == 'claimed' %}bg-gray-100 text-gray-800
                                    {% else %}bg-red-100 text-red-800{% endif %}">
                                    {{ appointment.get_status_display }}
                                </span>
                                <svg class="ml-2 h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                </svg>
                            </div>
                        </div>
                        {% if appointment.notes %}
                        <div class="mt-2">
                            <p class="text-sm text-gray-600">{{ appointment.notes|truncatewords:20 }}</p>
                        </div>
                        {% endif %}
                    </div>
                </a>
            </li>
            {% endfor %}
        </ul>
    </div>

    <!-- Pagination -->
    {% if is_paginated %}
    <nav class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 mt-6" aria-label="Pagination">
        <div class="hidden sm:block">
            <p class="text-sm text-gray-700">
                Showing
                <span class="font-medium">{{ page_obj.start_index }}</span>
                to
                <span class="font-medium">{{ page_obj.end_index }}</span>
                of
                <span class="font-medium">{{ paginator.count }}</span>
                results
            </p>
        </div>
        <div class="flex-1 flex justify-between sm:justify-end">
            {% if page_obj.has_previous %}
            <a href="?page={{ page_obj.previous_page_number }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                Previous
            </a>
            {% endif %}
            {% if page_obj.has_next %}
            <a href="?page={{ page_obj.next_page_number }}" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                Next
            </a>
            {% endif %}
        </div>
    </nav>
    {% endif %}

    {% else %}
    <!-- Empty state -->
    <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No appointments</h3>
        <p class="mt-1 text-sm text-gray-500">Get started by creating your first appointment.</p>
        <div class="mt-6">
            <a href="{% url 'appointment_create' %}" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.5v15m7.5-7.5h-15" />
                </svg>
                New Appointment
            </a>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
