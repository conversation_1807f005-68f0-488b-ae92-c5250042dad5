{% extends 'base.html' %}

{% block title %}Browse Services - JHCSC Unified Student Services{% endblock %}

{% block page_title %}Browse Services{% endblock %}

{% block extra_head %}
<!-- Custom Tailwind Config for Emerald & Gold Theme -->
<script>
    tailwind.config = {
        theme: {
            extend: {
                colors: {
                    emerald: {
                        50: '#ecfdf5',
                        100: '#d1fae5',
                        200: '#a7f3d0',
                        300: '#6ee7b7',
                        400: '#34d399',
                        500: '#10b981',
                        600: '#059669',
                        700: '#047857',
                        800: '#065f46',
                        900: '#064e3b',
                        950: '#022c22'
                    },
                    gold: {
                        50: '#fffbeb',
                        100: '#fef3c7',
                        200: '#fde68a',
                        300: '#fcd34d',
                        400: '#fbbf24',
                        500: '#f59e0b',
                        600: '#d97706',
                        700: '#b45309',
                        800: '#92400e',
                        900: '#78350f'
                    }
                },
                animation: {
                    'fade-in': 'fadeIn 0.6s ease-out',
                    'slide-up': 'slideUp 0.6s ease-out',
                    'card-hover': 'cardHover 0.3s ease',
                    'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                },
                keyframes: {
                    fadeIn: {
                        '0%': { opacity: '0' },
                        '100%': { opacity: '1' }
                    },
                    slideUp: {
                        '0%': { opacity: '0', transform: 'translateY(20px)' },
                        '100%': { opacity: '1', transform: 'translateY(0)' }
                    },
                    cardHover: {
                        '0%': { transform: 'translateY(0)' },
                        '100%': { transform: 'translateY(-4px)' }
                    }
                }
            }
        }
    }
</script>

<style>
    [x-cloak] { display: none !important; }

    .card-hover {
        transition: all 0.3s ease;
    }

    .card-hover:hover {
        transform: translateY(-4px);
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    .tab-active {
        background: linear-gradient(135deg, #059669, #f59e0b);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
</style>
{% endblock %}

{% block mobile_nav %}
{% include 'navigation/student_nav.html' %}
{% endblock %}

{% block desktop_nav %}
{% include 'navigation/student_nav.html' %}
{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto animate-fade-in">
    <!-- Header Section -->
    <div class="bg-gradient-to-r from-emerald-600 to-emerald-700 rounded-2xl p-6 mb-8 text-white">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold mb-2">Available Services</h1>
                <p class="text-emerald-100">Discover and learn about services offered by our departments</p>
            </div>
            <div class="hidden md:block">
                <div class="w-16 h-16 bg-white/10 rounded-full flex items-center justify-center">
                    <svg class="w-8 h-8 text-gold-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- Department Filter -->
    <div class="mb-8 animate-slide-up">
        <!-- Mobile Filter -->
        <div class="sm:hidden">
            <label for="department-select" class="block text-sm font-medium text-gray-700 mb-2">Filter by Department</label>
            <select id="department-select"
                    name="department"
                    class="block w-full rounded-xl border-2 border-gray-200 bg-white px-4 py-3 text-lg shadow-sm focus:border-emerald-500 focus:ring-emerald-500 transition-all duration-200"
                    onchange="filterByDepartment(this.value)">
                <option value="">All Departments</option>
                {% for department in departments %}
                <option value="{{ department.id }}" {% if selected_department == department.id|stringformat:"s" %}selected{% endif %}>
                    {{ department.name }}
                </option>
                {% endfor %}
            </select>
        </div>

        <!-- Desktop Filter -->
        <div class="hidden sm:block">
            <div class="bg-white shadow-lg rounded-2xl p-2 border border-gray-100">
                <nav class="flex flex-wrap gap-2" aria-label="Department Filter">
                    <a href="{% url 'service_list' %}"
                       class="{% if not selected_department %}bg-gradient-to-r from-emerald-600 to-emerald-700 text-white shadow-lg{% else %}text-gray-600 hover:text-emerald-600 hover:bg-emerald-50{% endif %} px-6 py-3 rounded-xl font-semibold text-sm transition-all duration-200 card-hover">
                        All Departments
                    </a>
                    {% for department in departments %}
                    <a href="{% url 'service_list' %}?department={{ department.id }}"
                       class="{% if selected_department == department.id|stringformat:"s" %}bg-gradient-to-r from-emerald-600 to-emerald-700 text-white shadow-lg{% else %}text-gray-600 hover:text-emerald-600 hover:bg-emerald-50{% endif %} px-6 py-3 rounded-xl font-semibold text-sm transition-all duration-200 card-hover">
                        {{ department.name }}
                    </a>
                    {% endfor %}
                </nav>
            </div>
        </div>
    </div>

    <!-- Services Grid -->
    {% if services %}
    <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
        {% for service in services %}
        <div class="bg-white overflow-hidden shadow rounded-lg hover:shadow-lg transition-shadow duration-200">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center">
                            <span class="text-sm font-medium text-indigo-600">{{ service.department.code }}</span>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <h3 class="text-lg font-medium text-gray-900">
                            <a href="{% url 'service_detail' service.pk %}" class="hover:text-indigo-600">
                                {{ service.name }}
                            </a>
                        </h3>
                        <p class="text-sm text-gray-500">{{ service.department.name }}</p>
                    </div>
                </div>
                <div class="mt-4">
                    <p class="text-sm text-gray-600">{{ service.description|truncatewords:20 }}</p>
                </div>
                <div class="mt-4 flex items-center justify-between">
                    <div class="flex items-center text-sm text-gray-500">
                        <svg class="flex-shrink-0 mr-1.5 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        {{ service.processing_time }}
                    </div>
                    <div class="flex items-center text-sm text-gray-500">
                        <svg class="flex-shrink-0 mr-1.5 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        {{ service.requirements.count }} requirement{{ service.requirements.count|pluralize }}
                    </div>
                </div>
                <div class="mt-6 flex space-x-3">
                    <a href="{% url 'service_detail' service.pk %}" 
                       class="flex-1 bg-white py-2 px-3 border border-gray-300 rounded-md shadow-sm text-sm leading-4 font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 text-center">
                        View Details
                    </a>
                    <a href="{% url 'appointment_create' %}?service={{ service.id }}" 
                       class="flex-1 bg-indigo-600 py-2 px-3 border border-transparent rounded-md shadow-sm text-sm leading-4 font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 text-center">
                        Request Service
                    </a>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    {% else %}
    <!-- Empty state -->
    <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No services available</h3>
        <p class="mt-1 text-sm text-gray-500">
            {% if selected_department %}
                No services are currently available for the selected department.
            {% else %}
                No services are currently available.
            {% endif %}
        </p>
        {% if selected_department %}
        <div class="mt-6">
            <a href="{% url 'service_list' %}" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                View All Services
            </a>
        </div>
        {% endif %}
    </div>
    {% endif %}
</div>

<script>
function filterByDepartment(departmentId) {
    if (departmentId) {
        window.location.href = '{% url "service_list" %}?department=' + departmentId;
    } else {
        window.location.href = '{% url "service_list" %}';
    }
}
</script>
{% endblock %}
